* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #ffffff;
    color: #1f1f1f;
    line-height: 1.5;
}

.container {
    max-width: 428px;
    margin: 0 auto;
    padding: 24px 32px;
    min-height: 100vh;
    position: relative;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 50px;
    height: 46px;
}

.logo-text {
    font-size: 24px;
    font-weight: 600;
    color: #4c0099;
}

.notification-icon {
    position: relative;
    width: 48px;
    height: 48px;
    background-color: rgba(31, 31, 31, 0.1);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bell-icon {
    width: 21px;
    height: 21px;
}

.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background-color: #ff5757;
    border-radius: 6px;
}

/* Main Title */
.main-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
    color: #1f1f1f;
}

/* Hero Section */
.hero-section {
    display: flex;
    gap: 16px;
    margin-bottom: 32px;
}

.hero-image {
    flex: 1;
    height: 200px;
    border-radius: 24px;
    overflow: hidden;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Category Tags */
.category-tags {
    display: flex;
    gap: 12px;
    margin-bottom: 32px;
    overflow-x: auto;
}

.tag {
    padding: 18px 25px;
    border-radius: 28px;
    font-size: 16px;
    font-weight: 500;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 130px;
    text-align: center;
}

.tag.active {
    background-color: #ff5757;
    color: #ffffff;
}

.tag:not(.active) {
    background-color: rgba(255, 87, 87, 0.1);
    color: #1f1f1f;
}

/* Podcast List */
.podcast-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 180px;
}

.podcast-item {
    display: flex;
    align-items: center;
    gap: 16px;
}

.podcast-image {
    width: 108px;
    height: 96px;
    border-radius: 16px;
    overflow: hidden;
    flex-shrink: 0;
}

.podcast-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.podcast-info {
    flex: 1;
}

.podcast-info h3 {
    font-size: 19px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #1f1f1f;
}

.podcast-author {
    font-size: 16px;
    color: rgba(31, 31, 31, 0.7);
    margin-bottom: 4px;
}

.podcast-duration {
    font-size: 16px;
    color: rgba(31, 31, 31, 0.7);
}

.play-button {
    width: 48px;
    height: 48px;
    background-color: rgba(76, 0, 153, 0.1);
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    background-color: rgba(76, 0, 153, 0.2);
}

.play-button img {
    width: 18px;
    height: 18px;
}

/* Bottom Navigation Container */
.bottom-nav-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 158px;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 1) 100%);
    pointer-events: none;
}

/* Bottom Navigation */
.bottom-nav {
    position: absolute;
    bottom: 32px;
    left: 50%;
    transform: translateX(-50%);
    width: 364px;
    height: 72px;
    background: rgba(76, 0, 153, 0.1);
    border-radius: 48px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    backdrop-filter: blur(10px);
    pointer-events: all;
}

.nav-item {
    position: relative;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.nav-item.active {
    opacity: 1;
}

.nav-item img {
    width: 26px;
    height: 26px;
}

.nav-indicator {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 5px;
    height: 5px;
    background-color: #4c0099;
    border-radius: 50%;
}

/* Responsive Design */
@media (max-width: 480px) {
    .container {
        padding: 20px 16px;
    }

    .hero-section {
        flex-direction: column;
        gap: 12px;
    }

    .hero-image {
        height: 150px;
    }

    .category-tags {
        flex-wrap: wrap;
    }

    .bottom-nav {
        width: calc(100% - 32px);
        left: 16px;
        transform: none;
    }
}
